import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import NiceModal from '@ebay/nice-modal-react'
import Link from 'next/link'
import React from 'react'
import { FaMinus, FaPlus, FaTrash, FaUsers } from 'react-icons/fa'
import { GoChecklist } from 'react-icons/go'

export function Actions({ triggerChildren, data }: { triggerChildren: React.ReactNode; data: Role }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{triggerChildren}</DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem className="focus:bg-destructive focus:text-destructive-foreground">
          <FaMinus className="mr-2 h-4 w-4" />
          Revoke
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
