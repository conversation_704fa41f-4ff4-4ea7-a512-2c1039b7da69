import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import NiceModal from '@ebay/nice-modal-react'
import Link from 'next/link'
import React from 'react'
import { FaPlus, FaTrash, FaUsers } from 'react-icons/fa'
import { GoChecklist } from 'react-icons/go'

export function Actions({ triggerChildren, data }: { triggerChildren: React.ReactNode; data: Role }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{triggerChildren}</DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => NiceModal.show('addMember', { data })}>
          <FaPlus className="mr-2 h-4 w-4" />
          Add Member
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${data.id}/members`}>
            <FaUsers className="mr-2 h-4 w-4" />
            Members
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={`/teams/${data.id}/roles/permissions`}>
            <GoChecklist className="mr-2 h-4 w-4" />
            Permissions
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem
          className="focus:bg-destructive focus:text-white"
          onClick={() => NiceModal.show('deleteRole', { role: data })}>
          <FaTrash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
