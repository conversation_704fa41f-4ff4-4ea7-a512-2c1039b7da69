import fs from 'fs';
import path from 'path';
const __dirname = path.resolve();
import { faker } from '@faker-js/faker';

const tasks = Array.from({ length: 100 }, () => ({
  id: faker.company.buzzNoun().toUpperCase(),
  name: faker.company.name(),
  description: faker.hacker.phrase(),
  avatar: faker.image.avatar(),
  members: faker.number.int({ min: 1, max: 100 }),
}));

fs.writeFileSync(
  path.join(__dirname, 'tasks.json'),
  JSON.stringify(tasks, null, 2),
);

console.log('✅ Tasks data generated.');
