'use client'

import { DataTable } from '@/components/custom-ui/data-table'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { useUser } from '@/lib/hooks/useUser'
import { buildQueryString } from '@/lib/utils'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { MoreHorizontal, Settings, Trash2, UserPlus, Wrench } from 'lucide-react'

import Link from 'next/link'
import { useState } from 'react'
import { FaExclamationCircle } from 'react-icons/fa'
import { FiPlusCircle } from 'react-icons/fi'
import { useDebounceCallback } from 'usehooks-ts'

import AddMemberModalIndex from '@/app/(panel)/members/teams/components/modals/add-member'
import AddTeamModal from '@/app/(panel)/members/teams/components/modals/add-team'
// import DeleteTeamModal from '@/app/(panel)/members/teams/components/modals/delete-team'
import NiceModal from '@ebay/nice-modal-react'

NiceModal.register('addTeam', AddTeamModal)
// NiceModal.register('deleteTeam', DeleteTeamModal)
NiceModal.register('addTeamMemberIndex', AddMemberModalIndex)

// Team type definition
interface Team {
  id: string
  name: string
  description?: string
  avatar?: string
  users_count?: number
}

// Define columns for the datatable
const columns: ColumnDef<Team>[] = [
  {
    accessorKey: 'name',
    header: 'Tên',
    cell: ({ row }) => {
      const team = row.original
      return (
        <div className="group flex items-center space-x-2">
          <span className="font-medium">{team.name}</span>
          <Button
            size="icon"
            variant="outline"
            className="h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100"
            asChild>
            <Link href={`/teams/${team.id}`}>
              <Wrench className="h-3 w-3" />
            </Link>
          </Button>
        </div>
      )
    },
  },
  {
    accessorKey: 'description',
    header: 'Mô tả',
    cell: ({ row }) => {
      const description = row.getValue('description') as string
      return <span className="text-muted-foreground">{description || '-'}</span>
    },
  },
  {
    accessorKey: 'avatar',
    header: () => <div className="text-center">Avatar</div>,
    cell: ({ row }) => {
      const team = row.original
      return (
        <div className="flex items-center justify-center">
          <Avatar className="h-6 w-6">
            <AvatarImage
              src={team.avatar}
              alt={team.name}
              className="rounded-full border"
            />
            <AvatarFallback>{team.name.charAt(0).toUpperCase()}</AvatarFallback>
          </Avatar>
        </div>
      )
    },
  },
  {
    accessorKey: 'users_count',
    header: () => <div className="text-center">Thành viên</div>,
    cell: ({ row }) => {
      const count = row.getValue('users_count') as number
      return <div className="text-center">{count || 0}</div>
    },
  },
  {
    id: 'actions',
    header: () => <div className="text-right"></div>,
    cell: ({ row }) => {
      const team = row.original
      return (
        <div className="text-right">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  NiceModal.show('addTeamMemberIndex', {
                    teamId: team.id,
                    callback: () => {},
                  })
                }}>
                <UserPlus className="size-4" />
                Thêm thành viên
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/teams/${team.id}`}>
                  <Settings className="size-4" />
                  Cài đặt
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => {
                  NiceModal.show('deleteTeam', {
                    teamId: team.id,
                    teamName: team.name,
                    callback: () => {},
                  })
                }}>
                <Trash2 className="size-4" />
                Xóa team
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )
    },
  },
]

export default function Component() {
  const { user } = useUser()
  const [search, setSearch] = useState('')
  const [inputValue, setInputValue] = useState('')
  const debounced = useDebounceCallback(setSearch, 500)

  const { isPending, error, data } = useQuery({
    queryKey: ['getTeams', search, user?.current_organization?.alias],
    queryFn: async () => {
      if (!user?.current_organization?.alias) {
        throw new Error('No organization context available')
      }

      const queryString = buildQueryString({
        filter: {
          name: search,
        },
      })

      return await queryFetchHelper(`/${user.current_organization.alias}/teams?${queryString}`)
    },
    enabled: !!user?.current_organization?.alias,
  })

  const showModal = (name: string) => {
    NiceModal.show(name, {
      callback: () => {
        // Refetch data after modal actions
      },
    }).then()
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Danh sách Teams</h3>
          <p className="text-muted-foreground text-sm">Quản lý các teams trong tổ chức của bạn.</p>
        </div>
        <Alert variant="destructive">
          <FaExclamationCircle className="h-4 w-4" />
          <AlertTitle>Lỗi!</AlertTitle>
          <AlertDescription>Không thể tải danh sách teams. Vui lòng thử lại sau.</AlertDescription>
        </Alert>
      </div>
    )
  }

  const teams = data?.data?.data || []

  // Filter teams based on search
  const filteredTeams = teams.filter((team: Team) => {
    if (!inputValue) return true
    return (
      team.name.toLowerCase().includes(inputValue.toLowerCase()) ||
      (team.description && team.description.toLowerCase().includes(inputValue.toLowerCase()))
    )
  })

  return (
    <div className="space-y-4">
      <div className="flex flex-col justify-between space-y-2 md:flex-row md:items-center">
        <div>
          <h3 className="text-lg font-semibold">Danh sách Teams</h3>
          <p className="text-muted-foreground text-sm">Quản lý các teams trong tổ chức của bạn.</p>
        </div>
        <div className="flex items-center justify-between space-x-2">
          <Input
            type="search"
            placeholder="Tìm kiếm teams..."
            className="w-[200px]"
            value={inputValue}
            onChange={(event) => {
              setInputValue(event.target.value)
              debounced(event.target.value)
            }}
          />
          <Button onClick={() => showModal('addTeam')}>
            <FiPlusCircle className="size-4" />
            Thêm Team
          </Button>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={filteredTeams}
        isLoading={isPending}
        emptyMessage={inputValue ? `Không tìm thấy team với từ khóa "${inputValue}"` : 'Chưa có team nào trong tổ chức'}
      />
    </div>
  )
}
